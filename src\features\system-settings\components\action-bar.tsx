'use client';

import React, { useCallback, useMemo } from 'react';

import dynamic from 'next/dynamic';

import {
  Bar<PERSON>hartBig,
  BookCopy,
  CheckCircle,
  Combine,
  Edit,
  ListChecks,
  Pause,
  Play,
  Printer,
  Send,
  Settings,
  Truck,
  Users,
  XCircle,
} from 'lucide-react';

import { IconButton } from '@/shared/components/icon-button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/shared/components/select';
// Import Plant type
import { useTaskSelectionState } from '@/shared/components/contexts/TaskSelectionContext';
import {
  defaultVolumeOptions,
  productTypeOptions,
  taskStatusOptions,
} from '@/infrastructure/api/mock/mock-data';
// appStore is not directly needed here for plants anymore, as plants are passed via props
import { useToast } from '@/shared/hooks/use-toast';
import { usePumpTruckDispatchModal } from '@/features/task-management/hooks/usePumpTruckDispatchModal';
import { useUiStore } from '@/infrastructure/storage/stores/uiStore';
import type { Plant } from '@/core/types';
import { EVENTS, globalEventEmitter } from '@/core/utils/eventEmitter';

const AnnounceVehicleArrivalModal = dynamic(() =>
  import('@/models/announce-vehicle-arrival-modal').then(mod => mod.AnnounceVehicleArrivalModal)
);

const PumpTruckDispatchModal = dynamic(() =>
  import('@/models/pump-truck-dispatch-modal').then(mod => mod.PumpTruckDispatchModal)
);

const TaskEditModal = dynamic(() =>
  import('@/models/task-edit-modal').then(mod => mod.TaskEditModal)
);

function ActionGroup({ children }: { children: React.ReactNode }) {
  return (
    <div className='flex items-center gap-0.5 p-0.5 bg-background rounded-md border'>
      {children}
    </div>
  );
}

interface ActionBarProps {
  plants: Plant[]; // Add plants prop
}

function ActionBar({ plants }: ActionBarProps) {
  // Destructure plants from props
  const { taskStatusFilter, setTaskStatusFilter } = useUiStore();
  const { selectedTask, selectedTaskId } = useTaskSelectionState();
  const { toast } = useToast();

  // 检查是否有选中的任务
  const hasSelectedTask = !!selectedTaskId;
  const selectedTaskStatus = selectedTask?.dispatchStatus;

  // 泵车发车单模态框
  const pumpTruckDispatchModal = usePumpTruckDispatchModal({
    onDispatchConfirm: async (task, dispatchData) => {
      // 处理泵车发车确认逻辑
      console.log('泵车发车确认:', { task, dispatchData });
      toast({
        title: '泵车发车成功',
        description: `泵车 ${dispatchData.pumpTruckNumber} 已成功发车到任务 ${task.taskNumber}`,
      });
    },
  });

  // 任务编辑模态框状态
  const [isTaskEditModalOpen, setIsTaskEditModalOpen] = React.useState(false);

  const handleTaskEdit = useCallback(() => {
    if (!hasSelectedTask) {
      toast({
        title: '请先选择任务',
        description: '请在任务列表中选择一个任务后再进行编辑',
        variant: 'destructive',
      });
      return;
    }
    setIsTaskEditModalOpen(true);
  }, [hasSelectedTask, toast]);

  const handleTaskEditConfirm = useCallback(
    (taskData: any) => {
      console.log('任务编辑确认:', taskData);
      toast({
        title: '任务编辑成功',
        description: `任务 ${taskData.taskNumber} 已成功更新`,
      });
      setIsTaskEditModalOpen(false);
    },
    [toast]
  );

  // 任务状态操作处理函数
  const handletdj8qy7p5 = useCallback(() => {
    if (!hasSelectedTask) return;
    console.log('准备生产:', selectedTask?.taskNumber);
    toast({
      title: '任务状态更新',
      description: `任务 ${selectedTask?.taskNumber} 已设置为准备生产`,
    });
  }, [hasSelectedTask, selectedTask, toast]);

  const handleq352stax2 = useCallback(() => {
    if (!hasSelectedTask) return;
    console.log('转到正在进行:', selectedTask?.taskNumber);
    toast({
      title: '任务状态更新',
      description: `任务 ${selectedTask?.taskNumber} 已转到正在进行`,
    });
  }, [hasSelectedTask, selectedTask, toast]);

  const handlexlckugr46 = useCallback(() => {
    if (!hasSelectedTask) return;
    console.log('暂停任务:', selectedTask?.taskNumber);
    toast({
      title: '任务状态更新',
      description: `任务 ${selectedTask?.taskNumber} 已暂停`,
    });
  }, [hasSelectedTask, selectedTask, toast]);

  const handlelogtsi5s2 = useCallback(() => {
    if (!hasSelectedTask) return;
    console.log('完成任务:', selectedTask?.taskNumber);
    toast({
      title: '任务状态更新',
      description: `任务 ${selectedTask?.taskNumber} 已完成`,
    });
  }, [hasSelectedTask, selectedTask, toast]);

  const handleplk4f5p4e = useCallback(() => {
    if (!hasSelectedTask) return;
    console.log('撤销任务:', selectedTask?.taskNumber);
    toast({
      title: '任务状态更新',
      description: `任务 ${selectedTask?.taskNumber} 已撤销`,
    });
  }, [hasSelectedTask, selectedTask, toast]);

  // 车辆调度处理函数
  const handlejjl9l77rj = useCallback(() => {
    console.log('🎯 发送生产指令按钮被点击:', {
      hasSelectedTask,
      selectedTaskId,
      selectedTaskNumber: selectedTask?.taskNumber,
      selectedTaskStatus,
    });

    if (!hasSelectedTask || !selectedTaskId) {
      console.warn('⚠️ 没有选中的任务，无法发送生产指令');
      return;
    }

    console.log('📡 发射全局事件:', EVENTS.SEND_PRODUCTION_INSTRUCTION, selectedTaskId);

    // 发射全局事件来触发罐车发车单模态框
    globalEventEmitter.emit(EVENTS.SEND_PRODUCTION_INSTRUCTION, selectedTaskId);

    console.log('✅ 全局事件已发射');
  }, [hasSelectedTask, selectedTask, selectedTaskId, selectedTaskStatus]);

  const handleo12o0qpdp = useCallback(() => {
    if (!hasSelectedTask || !selectedTask) return;
    console.log('安排泵车:', selectedTask.taskNumber);
    pumpTruckDispatchModal.openModal(selectedTask);
  }, [hasSelectedTask, selectedTask, pumpTruckDispatchModal]);

  const handle0hzwc1vf3 = useCallback(() => {
    console.log('其他车辆出厂');
    toast({
      title: '车辆调度',
      description: `其他车辆出厂`,
    });
  }, [hasSelectedTask, selectedTask, toast]);

  // 任务详情和统计处理函数
  const handleagn0bknvz = useCallback(() => {
    if (!hasSelectedTask) return;
    console.log('发车明细:', selectedTask?.taskNumber);
    toast({
      title: '发车明细',
      description: `正在查看任务 ${selectedTask?.taskNumber} 的发车明细`,
    });
  }, [hasSelectedTask, selectedTask, toast]);

  const handlemniu97bow = useCallback(() => {
    if (!hasSelectedTask) return;
    console.log('生产进度:', selectedTask?.taskNumber);
    globalEventEmitter.emit(EVENTS.OPEN_TASK_PROGRESS_MODAL, selectedTask);
  }, [hasSelectedTask, selectedTask]);

  // 系统设置处理函数
  const handlegzp8gf0fu = useCallback(() => {
    console.log('系统参数设置');
    toast({
      title: '系统设置',
      description: '正在打开系统参数设置',
    });
  }, [toast]);

  // 班次管理处理函数
  const handle04sxshxb3 = useCallback(() => {
    console.log('换班');
    toast({
      title: '换班',
      description: '正在进行换班操作',
    });
  }, [toast]);

  const handlernbnn23na = useCallback(() => {
    console.log('交接班记录');
    toast({
      title: '交接班记录',
      description: '正在查看交接班记录',
    });
  }, [toast]);

  // 统计报表处理函数
  const handleom6583ao0 = useCallback(() => {
    console.log('罐车出车统计');
    toast({
      title: '罐车出车统计',
      description: '正在生成罐车出车统计报表',
    });
  }, [toast]);

  const handlel2nvk3io2 = useCallback(() => {
    console.log('调度工程统计');
    toast({
      title: '调度工程统计',
      description: '正在生成调度工程统计报表',
    });
  }, [toast]);

  const handlecssmmtjvz = useCallback(() => {
    console.log('泵车出车统计');
    toast({
      title: '泵车出车统计',
      description: '正在生成泵车出车统计报表',
    });
  }, [toast]);

  return (
    <div className='flex items-center space-x-0.5 flex-wrap gap-0.5'>
      {/* 选中任务状态指示器 */}
      {hasSelectedTask ? (
        <ActionGroup>
          <div className='px-3 py-1 text-xs bg-primary text-primary-foreground rounded border flex items-center gap-2'>
            <div className='w-2 h-2 bg-primary-foreground rounded-full'></div>
            已选中: {selectedTask?.taskNumber}
            <span className='text-primary-foreground/70'>({selectedTask?.dispatchStatus})</span>
          </div>
        </ActionGroup>
      ) : (
        <ActionGroup>
          <div className='px-3 py-1 text-xs bg-muted text-muted-foreground rounded border flex items-center gap-2'>
            <div className='w-2 h-2 bg-muted-foreground/50 rounded-full'></div>
            未选中任务
          </div>
        </ActionGroup>
      )}

      <ActionGroup>
        <Select value={taskStatusFilter} onValueChange={setTaskStatusFilter}>
          <SelectTrigger className='w-[110px] h-7 text-xs px-1.5'>
            <SelectValue placeholder='任务状态过滤' />
          </SelectTrigger>
          <SelectContent>
            {taskStatusOptions.map(opt => (
              <SelectItem key={opt.value} value={opt.value} className='text-xs'>
                {opt.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </ActionGroup>

      <ActionGroup>
        <IconButton
          icon={Play}
          tooltipLabel={
            hasSelectedTask ? `准备生产 - ${selectedTask?.taskNumber}` : '准备生产 (请先选择任务)'
          }
          onClick={handletdj8qy7p5}
          disabled={!hasSelectedTask}
        />
        <IconButton
          icon={Play}
          tooltipLabel={
            hasSelectedTask
              ? `转到正在进行 - ${selectedTask?.taskNumber}`
              : '转到正在进行 (请先选择任务)'
          }
          onClick={handleq352stax2}
          disabled={!hasSelectedTask}
        />
        <IconButton
          icon={Pause}
          tooltipLabel={
            hasSelectedTask ? `暂停任务 - ${selectedTask?.taskNumber}` : '暂停任务 (请先选择任务)'
          }
          onClick={handlexlckugr46}
          disabled={!hasSelectedTask || selectedTaskStatus !== 'InProgress'}
        />
        <IconButton
          icon={CheckCircle}
          tooltipLabel={
            hasSelectedTask ? `完成任务 - ${selectedTask?.taskNumber}` : '完成任务 (请先选择任务)'
          }
          onClick={handlelogtsi5s2}
          disabled={!hasSelectedTask || selectedTaskStatus !== 'InProgress'}
        />
        <IconButton
          icon={XCircle}
          tooltipLabel={
            hasSelectedTask ? `撤销任务 - ${selectedTask?.taskNumber}` : '撤销任务 (请先选择任务)'
          }
          onClick={handleplk4f5p4e}
          disabled={!hasSelectedTask}
        />
      </ActionGroup>

      <ActionGroup>
        <IconButton
          icon={Send}
          tooltipLabel={
            hasSelectedTask
              ? `发送生产指令 - ${selectedTask?.taskNumber}`
              : '发送生产指令 (请先选择任务)'
          }
          onClick={handlejjl9l77rj}
          disabled={!hasSelectedTask}
        />
        <IconButton
          icon={Truck}
          tooltipLabel={
            hasSelectedTask ? `安排泵车 - ${selectedTask?.taskNumber}` : '安排泵车 (请先选择任务)'
          }
          onClick={handleo12o0qpdp}
          disabled={!hasSelectedTask}
        />
        <IconButton
          icon={Combine}
          tooltipLabel={
            '安排其他车辆出厂'
          }
          onClick={handle0hzwc1vf3}
          disabled={false}
        />
      </ActionGroup>

      <ActionGroup>
        <IconButton
          icon={Edit}
          tooltipLabel={
            hasSelectedTask ? `修改任务 - ${selectedTask?.taskNumber}` : '修改任务 (请先选择任务)'
          }
          onClick={handleTaskEdit}
          disabled={!hasSelectedTask}
        />
        <IconButton
          icon={ListChecks}
          tooltipLabel={
            hasSelectedTask ? `发车明细 - ${selectedTask?.taskNumber}` : '发车明细 (请先选择任务)'
          }
          onClick={handleagn0bknvz}
          disabled={!hasSelectedTask}
        />
        <IconButton
          icon={BarChartBig}
          tooltipLabel={
            hasSelectedTask ? `生产进度 - ${selectedTask?.taskNumber}` : '生产进度 (请先选择任务)'
          }
          onClick={handlemniu97bow}
          disabled={!hasSelectedTask}
        />
      </ActionGroup>

      <ActionGroup>
        {/* Use plants from props */}
        {plants && plants.length > 0 && <AnnounceVehicleArrivalModal plants={plants} />}
        <IconButton icon={Settings} tooltipLabel='系统参数设置' onClick={handlegzp8gf0fu} />
      </ActionGroup>

      <ActionGroup>
        <IconButton icon={Users} tooltipLabel='换班' onClick={handle04sxshxb3} />
        <IconButton icon={BookCopy} tooltipLabel='交接班记录' onClick={handlernbnn23na} />
      </ActionGroup>

      <ActionGroup>
        <IconButton icon={Printer} tooltipLabel='罐车出车统计' onClick={handleom6583ao0} />
        <IconButton icon={BarChartBig} tooltipLabel='调度工程统计' onClick={handlel2nvk3io2} />
        <IconButton icon={Truck} tooltipLabel='泵车出车统计' onClick={handlecssmmtjvz} />
      </ActionGroup>

      <ActionGroup>
        <Select defaultValue='Normal'>
          <SelectTrigger className='w-[100px] h-7 text-xs px-1.5'>
            <SelectValue placeholder='缺省方量' />
          </SelectTrigger>
          <SelectContent>
            {defaultVolumeOptions.map(opt => (
              <SelectItem key={opt.value} value={opt.value} className='text-xs'>
                {opt.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        <Select defaultValue='All'>
          <SelectTrigger className='w-[110px] h-7 text-xs px-1.5'>
            <SelectValue placeholder='产品种类过滤' />
          </SelectTrigger>
          <SelectContent>
            {productTypeOptions.map(opt => (
              <SelectItem key={opt.value} value={opt.value} className='text-xs'>
                {opt.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </ActionGroup>

      {/* 泵车发车单模态框 */}
      <PumpTruckDispatchModal
        open={pumpTruckDispatchModal.isOpen}
        onOpenChange={pumpTruckDispatchModal.closeModal}
        task={pumpTruckDispatchModal.task || undefined}
        onConfirm={pumpTruckDispatchModal.handleConfirm}
      />

      {/* 任务编辑模态框 */}
      <TaskEditModal
        open={isTaskEditModalOpen}
        onOpenChange={setIsTaskEditModalOpen}
        task={selectedTask || undefined}
        onConfirm={handleTaskEditConfirm}
      />
    </div>
  );
}

const MemoizedActionBar = React.memo(ActionBar);
export { MemoizedActionBar as ActionBar };
