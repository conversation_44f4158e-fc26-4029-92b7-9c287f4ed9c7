'use client';

import React, { useState } from 'react';

import { useDrop } from 'react-dnd';
import {
  AlertTriangle,
  Bookmark,
  Calculator,
  CheckCircle,
  ChevronDown,
  ChevronUp,
  Container,
  Droplets,
  FileText,
  Mountain,
  Plus,
  RotateCcw,
  Shield,
  Target,
  Trash2,
  Zap,
  Sparkles,
} from 'lucide-react';

import { Badge } from '@/shared/components/badge';
import { Button } from '@/shared/components/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/shared/components/card';
import { Input } from '@/shared/components/input';
import { Label } from '@/shared/components/label';
import { cn } from '@/core/lib/utils';

// 导入统一的类型定义
import type { RatioMaterial } from '@/core/types/ratio';
import {
  RatioDragItemTypes,
  useRatioDragDrop,
  DragIndicator,
  type DraggedSiloMaterial,
} from '@/shared/components/contexts/RatioDragDropContext';
import { CalculationButtons } from './calculation-buttons';

interface CalculationParams {
  // 基础参数
  density: number;
  waterRatio: number;
  waterAmount: number;
  sandRatio: number;

  // 胶凝材料参数
  cementAmount: number;
  flyashRatio: number;
  mineralPowderRatio: number;
  silicaFumeRatio: number;

  // 外加剂参数
  additiveRatio: number;
  antifreezeRatio: number;
  expansionRatio: number;
  earlyStrengthRatio: number;

  // 特殊材料参数
  ultraFineSandRatio: number;
  s105Ratio: number;

  // 工艺参数
  slump: number;
  airContent: number;
  strengthGrade: number;
}

interface RatioDesignPanelProps {
  ratioMaterials: RatioMaterial[];
  onMaterialsChangeAction: (materials: RatioMaterial[]) => void;
  calculationParams: CalculationParams;
  onParamsChangeAction: (params: CalculationParams) => void;
  onCalculate: () => void;
  onReverseCalculate: () => void;
  canCalculate: boolean;
  canReverseCalculate: boolean;
  isCalculating: boolean;
  hasParamsChanged?: boolean; // 新增：参数是否已修改
  onSelectRatio?: () => void;
  onCheckStandard?: () => void;
  onApplyNotification?: () => void;
  onOpenRecommendation?: () => void;
  onSaveBackupRatio?: () => void; // 新增：存为备选配比
}

function EditableNumberInput({
  value,
  onChange,
  min = 0,
  max = 9999,
  step = 1,
  className = '',
}: {
  value: number;
  onChange: (value: number) => void;
  min?: number;
  max?: number;
  step?: number;
  className?: string;
}) {
  const handleIncrement = () => {
    const newValue = Math.min(max, value + step);
    onChange(newValue);
  };

  const handleDecrement = () => {
    const newValue = Math.max(min, value - step);
    onChange(newValue);
  };

  return (
    <div className='flex items-center gap-1'>
      <Input
        type='number'
        value={value}
        onChange={e => onChange(parseFloat(e.target.value) || 0)}
        min={min}
        max={max}
        step={step}
        className={cn('text-center font-mono', className)}
      />
      <div className='flex flex-col'>
        <Button
          variant='ghost'
          size='sm'
          onClick={handleIncrement}
          className='h-4 w-6 p-0 hover:bg-muted'
        >
          <ChevronUp className='h-3 w-3' />
        </Button>
        <Button
          variant='ghost'
          size='sm'
          onClick={handleDecrement}
          className='h-4 w-6 p-0 hover:bg-muted'
        >
          <ChevronDown className='h-3 w-3' />
        </Button>
      </div>
    </div>
  );
}

function MaterialRow({
  material,
  onUpdate,
  onDelete,
}: {
  material: RatioMaterial;
  onUpdate: (id: string, updates: Partial<RatioMaterial>) => void;
  onDelete: (id: string) => void;
}) {
  const getTypeIcon = (name: string) => {
    if (name.includes('水泥')) return <Container className='h-3 w-3 text-gray-600' />;
    if (name.includes('水')) return <Droplets className='h-3 w-3 text-blue-600' />;
    if (name.includes('砂')) return <Mountain className='h-3 w-3 text-yellow-600' />;
    if (name.includes('石')) return <Mountain className='h-3 w-3 text-stone-600' />;
    if (name.includes('外加剂') || name.includes('减水剂'))
      return <Zap className='h-3 w-3 text-purple-600' />;
    return <Container className='h-3 w-3 text-gray-600' />;
  };

  // 检查是否是新添加的材料（最近1秒内创建的）
  const isNewMaterial = Date.now() - parseInt(material.id.split('-')[1] || '0') < 1000;

  // 检查料仓状态（如果材料有siloStatus属性）
  const siloStatus = (material as any).siloStatus;
  const isSiloMissing = siloStatus === 'missing';

  return (
    <div
      className={cn(
        'grid grid-cols-8 gap-2 p-2 border rounded hover:bg-muted/30 transition-all duration-500',
        isNewMaterial &&
        'animate-in slide-in-from-left-5 fade-in-0 bg-green-50 dark:bg-green-950/20 border-green-200 dark:border-green-800',
        isSiloMissing && 'border-red-500 bg-red-50 dark:bg-red-950/20 shadow-md'
      )}
    >
      {/* 材料名称 */}
      <div className='flex items-center gap-1'>
        {getTypeIcon(material.name)}
        <div className='min-w-0 flex-1'>
          <div className='flex items-center gap-1'>
            <p className='font-medium text-xs truncate'>{material.name}</p>
            {isSiloMissing && (
              <div title='料仓中无此材料'>
                <AlertTriangle className='h-3 w-3 text-red-500 flex-shrink-0' />
              </div>
            )}
          </div>
          <p className='text-xs text-muted-foreground truncate'>{material.specification}</p>
        </div>
      </div>

      {/* 理论量 */}
      <div className='flex flex-col'>
        <Label className='text-xs text-muted-foreground mb-0.5'>理论量</Label>
        <Input
          type='number'
          value={material.theoreticalAmount}
          onChange={e =>
            onUpdate(material.id, { theoreticalAmount: parseFloat(e.target.value) || 0 })
          }
          className='h-6 text-xs p-1'
        />
      </div>

      {/* 含水率 */}
      <div className='flex flex-col'>
        <Label className='text-xs text-muted-foreground mb-0.5'>含水率</Label>
        <Input
          type='number'
          value={material.waterContent}
          onChange={e => onUpdate(material.id, { waterContent: parseFloat(e.target.value) || 0 })}
          className='h-6 text-xs p-1'
          step={0.1}
          max={100}
        />
      </div>

      {/* 含石率 */}
      <div className='flex flex-col'>
        <Label className='text-xs text-muted-foreground mb-0.5'>含石率</Label>
        <Input
          type='number'
          value={material.stoneContent}
          onChange={e => onUpdate(material.id, { stoneContent: parseFloat(e.target.value) || 0 })}
          className='h-6 text-xs p-1'
          step={0.1}
          max={100}
        />
      </div>

      {/* 实际量 */}
      <div className='flex flex-col'>
        <Label className='text-xs text-muted-foreground mb-0.5'>实际量</Label>
        <div className='h-6 flex items-center'>
          <span className='text-xs font-mono text-blue-600'>
            {material.actualAmount.toFixed(1)}
          </span>
        </div>
      </div>

      {/* 设计值 */}
      <div className='flex flex-col'>
        <Label className='text-xs text-muted-foreground mb-0.5'>设计值</Label>
        <div className='h-6 flex items-center'>
          <span className='text-xs font-mono font-bold text-red-600'>
            {material.designValue.toFixed(1)}
          </span>
        </div>
      </div>

      {/* 料仓 */}
      <div className='flex flex-col'>
        <Label className='text-xs text-muted-foreground mb-0.5'>料仓</Label>
        <div className='h-6 flex items-center'>
          <Badge variant='outline' className='text-xs py-0 px-1 h-5'>
            {material.siloId.replace('silo-', '')}
          </Badge>
        </div>
      </div>

      {/* 操作 */}
      <div className='flex items-center justify-center'>
        <Button
          variant='ghost'
          size='sm'
          onClick={() => onDelete(material.id)}
          className='h-6 w-6 p-0 text-red-500 hover:text-red-700 hover:bg-red-50'
        >
          <Trash2 className='h-3 w-3' />
        </Button>
      </div>
    </div>
  );
}

export function RatioDesignPanel({
  ratioMaterials,
  onMaterialsChangeAction,
  calculationParams,
  onParamsChangeAction,
  onCalculate,
  onReverseCalculate,
  canCalculate,
  canReverseCalculate,
  isCalculating,
  hasParamsChanged = false,
  onSelectRatio,
  onCheckStandard,
  onApplyNotification,
  onOpenRecommendation,
  onSaveBackupRatio,
}: RatioDesignPanelProps) {
  const { onMaterialDrop } = useRatioDragDrop();

  const [{ isOver, canDrop }, drop] = useDrop({
    accept: RatioDragItemTypes.SILO_MATERIAL,
    drop: (item: DraggedSiloMaterial) => {
      // 检查是否已存在相同材料（基于材料名称和规格）
      const existingMaterial = ratioMaterials.find(
        material =>
          material.name === item.name && material.specification === (item.specification || '')
      );

      if (existingMaterial) {
        // 如果材料已存在，不添加重复材料，显示提示
        console.log('材料已存在，跳过添加:', item.name);
        return;
      }

      // 只通知拖拽上下文，不在这里添加材料
      // 材料添加逻辑由 page.tsx 中的 handleMaterialDrop 处理
      onMaterialDrop(item);
    },
    collect: monitor => ({
      isOver: monitor.isOver(),
      canDrop: monitor.canDrop(),
    }),
  });

  const handleMaterialUpdate = (id: string, updates: Partial<RatioMaterial>) => {
    const updatedMaterials = ratioMaterials.map(material =>
      material.id === id ? { ...material, ...updates } : material
    );
    onMaterialsChangeAction(updatedMaterials);
  };

  const handleMaterialDelete = (id: string) => {
    const updatedMaterials = ratioMaterials.filter(material => material.id !== id);
    onMaterialsChangeAction(updatedMaterials);
  };

  const totalWeight = ratioMaterials.reduce((sum, material) => sum + material.designValue, 0);

  return (
    <div className='space-y-4'>
      {/* 紧凑计算参数 */}
      <Card>
        <CardHeader className='pb-2 pt-2'>
          <CardTitle className='text-sm flex items-center gap-2 font-medium'>
            <Calculator className='h-4 w-4 text-primary' />
            计算参数
          </CardTitle>
        </CardHeader>
        <CardContent className='p-2'>
          {/* 第一行：比率类参数 */}
          <div className='mb-2'>
            <Label className='text-xs text-muted-foreground mb-1 block'>比率参数 (%)</Label>
            <div className='grid grid-cols-8 gap-2'>
              <div>
                <Label className='text-xs text-muted-foreground mb-1'>水胶比</Label>
                <Input
                  type='number'
                  value={calculationParams.waterRatio}
                  onChange={e =>
                    onParamsChangeAction({
                      ...calculationParams,
                      waterRatio: parseFloat(e.target.value) || 0.45,
                    })
                  }
                  className='h-6 text-xs'
                  step={0.01}
                  min={0.2}
                  max={0.8}
                />
              </div>

              <div>
                <Label className='text-xs text-muted-foreground mb-1'>砂率</Label>
                <Input
                  type='number'
                  value={calculationParams.sandRatio}
                  onChange={e =>
                    onParamsChangeAction({
                      ...calculationParams,
                      sandRatio: parseFloat(e.target.value) || 35,
                    })
                  }
                  className='h-6 text-xs'
                  min={20}
                  max={60}
                />
              </div>

              <div>
                <Label className='text-xs text-muted-foreground mb-1'>外加剂</Label>
                <Input
                  type='number'
                  value={calculationParams.additiveRatio}
                  onChange={e =>
                    onParamsChangeAction({
                      ...calculationParams,
                      additiveRatio: parseFloat(e.target.value) || 1.2,
                    })
                  }
                  className='h-6 text-xs'
                  step={0.1}
                  min={0}
                  max={5}
                />
              </div>

              <div>
                <Label className='text-xs text-muted-foreground mb-1'>粉煤灰</Label>
                <Input
                  type='number'
                  value={calculationParams.flyashRatio}
                  onChange={e =>
                    onParamsChangeAction({
                      ...calculationParams,
                      flyashRatio: parseFloat(e.target.value) || 15,
                    })
                  }
                  className='h-6 text-xs'
                  step={1}
                  min={0}
                  max={50}
                />
              </div>

              <div>
                <Label className='text-xs text-muted-foreground mb-1'>矿粉</Label>
                <Input
                  type='number'
                  value={calculationParams.mineralPowderRatio}
                  onChange={e =>
                    onParamsChangeAction({
                      ...calculationParams,
                      mineralPowderRatio: parseFloat(e.target.value) || 0,
                    })
                  }
                  className='h-6 text-xs'
                  step={1}
                  min={0}
                  max={30}
                />
              </div>

              <div>
                <Label className='text-xs text-muted-foreground mb-1'>硅灰</Label>
                <Input
                  type='number'
                  value={calculationParams.silicaFumeRatio}
                  onChange={e =>
                    onParamsChangeAction({
                      ...calculationParams,
                      silicaFumeRatio: parseFloat(e.target.value) || 0,
                    })
                  }
                  className='h-6 text-xs'
                  step={1}
                  min={0}
                  max={15}
                />
              </div>

              <div>
                <Label className='text-xs text-muted-foreground mb-1'>防冻剂</Label>
                <Input
                  type='number'
                  value={calculationParams.antifreezeRatio}
                  onChange={e =>
                    onParamsChangeAction({
                      ...calculationParams,
                      antifreezeRatio: parseFloat(e.target.value) || 0,
                    })
                  }
                  className='h-6 text-xs'
                  step={0.1}
                  min={0}
                  max={10}
                />
              </div>

              <div>
                <Label className='text-xs text-muted-foreground mb-1'>膨胀剂</Label>
                <Input
                  type='number'
                  value={calculationParams.expansionRatio}
                  onChange={e =>
                    onParamsChangeAction({
                      ...calculationParams,
                      expansionRatio: parseFloat(e.target.value) || 0,
                    })
                  }
                  className='h-6 text-xs'
                  step={1}
                  min={0}
                  max={15}
                />
              </div>
            </div>
          </div>

          {/* 第二行：数量类参数 */}
          <div>
            <Label className='text-xs text-muted-foreground mb-1 block'>
              数量参数 (kg/m³, mm, MPa)
            </Label>
            <div className='grid grid-cols-8 gap-2'>
              <div>
                <Label className='text-xs text-muted-foreground mb-1'>水泥用量</Label>
                <Input
                  type='number'
                  value={calculationParams.cementAmount}
                  onChange={e =>
                    onParamsChangeAction({
                      ...calculationParams,
                      cementAmount: parseFloat(e.target.value) || 350,
                    })
                  }
                  className='h-6 text-xs'
                  step={10}
                  min={200}
                  max={600}
                />
              </div>

              <div>
                <Label className='text-xs text-muted-foreground mb-1'>用水量</Label>
                <Input
                  type='number'
                  value={calculationParams.waterAmount}
                  onChange={e =>
                    onParamsChangeAction({
                      ...calculationParams,
                      waterAmount: parseFloat(e.target.value) || 180,
                    })
                  }
                  className='h-6 text-xs'
                  min={100}
                  max={300}
                />
              </div>

              <div>
                <Label className='text-xs text-muted-foreground mb-1'>密度</Label>
                <Input
                  type='number'
                  value={calculationParams.density}
                  onChange={e =>
                    onParamsChangeAction({
                      ...calculationParams,
                      density: parseFloat(e.target.value) || 2.4,
                    })
                  }
                  className='h-6 text-xs'
                  step={0.01}
                  min={2.0}
                  max={3.0}
                />
              </div>

              <div>
                <Label className='text-xs text-muted-foreground mb-1'>坍落度</Label>
                <Input
                  type='number'
                  value={calculationParams.slump}
                  onChange={e =>
                    onParamsChangeAction({
                      ...calculationParams,
                      slump: parseFloat(e.target.value) || 180,
                    })
                  }
                  className='h-6 text-xs'
                  step={10}
                  min={50}
                  max={250}
                />
              </div>

              <div>
                <Label className='text-xs text-muted-foreground mb-1'>含气量</Label>
                <Input
                  type='number'
                  value={calculationParams.airContent}
                  onChange={e =>
                    onParamsChangeAction({
                      ...calculationParams,
                      airContent: parseFloat(e.target.value) || 4.5,
                    })
                  }
                  className='h-6 text-xs'
                  step={0.5}
                  min={2}
                  max={8}
                />
              </div>

              <div>
                <Label className='text-xs text-muted-foreground mb-1'>强度等级</Label>
                <Input
                  type='number'
                  value={calculationParams.strengthGrade}
                  onChange={e =>
                    onParamsChangeAction({
                      ...calculationParams,
                      strengthGrade: parseFloat(e.target.value) || 30,
                    })
                  }
                  className='h-6 text-xs'
                  step={5}
                  min={15}
                  max={80}
                />
              </div>

              <div>
                <Label className='text-xs text-muted-foreground mb-1'>超细砂</Label>
                <Input
                  type='number'
                  value={calculationParams.ultraFineSandRatio}
                  onChange={e =>
                    onParamsChangeAction({
                      ...calculationParams,
                      ultraFineSandRatio: parseFloat(e.target.value) || 0,
                    })
                  }
                  className='h-6 text-xs'
                  step={1}
                  min={0}
                  max={20}
                />
              </div>

              <div>
                <Label className='text-xs text-muted-foreground mb-1'>早强剂</Label>
                <Input
                  type='number'
                  value={calculationParams.earlyStrengthRatio}
                  onChange={e =>
                    onParamsChangeAction({
                      ...calculationParams,
                      earlyStrengthRatio: parseFloat(e.target.value) || 0,
                    })
                  }
                  className='h-6 text-xs'
                  step={0.1}
                  min={0}
                  max={5}
                />
              </div>
            </div>
          </div>

          {/* 紧凑总重量显示 */}
          <div className='mt-2 p-1 bg-blue-50 dark:bg-blue-950/20 rounded border border-blue-200 dark:border-blue-800'>
            <div className='flex items-center justify-between text-xs'>
              <span className='text-blue-700 dark:text-blue-300'>总重量:</span>
              <span className='font-mono font-bold text-blue-700 dark:text-blue-300'>
                {totalWeight.toFixed(2)} kg/m³
              </span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 计算和反算按钮 */}
      <CalculationButtons
        onCalculate={onCalculate}
        onReverseCalculate={onReverseCalculate}
        canCalculate={canCalculate}
        canReverseCalculate={canReverseCalculate}
        isCalculating={isCalculating}
        hasParamsChanged={hasParamsChanged}
      />

      {/* 紧凑配比设计区域 */}
      <Card>
        <CardHeader className='pb-2 pt-2'>
          <div className='flex items-center justify-between'>
            <CardTitle className='text-sm flex items-center gap-2 font-medium'>
              <Target className='h-4 w-4 text-primary' />
              配比设计
              <Badge variant='outline' className='text-xs ml-2'>
                {ratioMaterials.length} 种物料
              </Badge>
            </CardTitle>

            {/* 功能按钮组 */}
            <div className='flex items-center gap-1'>
              <Button
                variant='outline'
                size='sm'
                className='text-xs h-7 gap-1 border-green-200 text-green-600 hover:bg-green-50'
                onClick={onSelectRatio}
              >
                <CheckCircle className='h-3 w-3' />
                选用配比
              </Button>

              <Button
                variant='outline'
                size='sm'
                className='text-xs h-7 gap-1 border-blue-200 text-blue-600 hover:bg-blue-50'
                onClick={onCheckStandard}
              >
                <Shield className='h-3 w-3' />
                检查标准
              </Button>

              <Button
                variant='outline'
                size='sm'
                className='text-xs h-7 gap-1 border-purple-200 text-purple-600 hover:bg-purple-50'
                onClick={onApplyNotification}
              >
                <FileText className='h-3 w-3' />
                应用通知单
              </Button>

              <Button
                variant='outline'
                size='sm'
                className='text-xs h-7 gap-1 border-orange-200 text-orange-600 hover:bg-orange-50'
                onClick={onSaveBackupRatio}
                disabled={ratioMaterials.length === 0}
              >
                <Bookmark className='h-3 w-3' />
                存为备选
              </Button>

              <Button
                variant='outline'
                size='sm'
                className='text-xs h-7 gap-1 border-purple-200 text-purple-600 hover:bg-purple-50'
                onClick={onOpenRecommendation}
              >
                <Sparkles className='h-3 w-3' />
                AI推荐
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent className='p-2'>
          <div
            ref={drop as any}
            className={cn(
              'min-h-[200px] border border-dashed rounded transition-all duration-200 relative',
              isOver ? 'border-primary bg-primary/5 shadow-inner' : 'border-muted-foreground/30',
              ratioMaterials.length === 0 && 'flex items-center justify-center'
            )}
          >
            {/* 拖拽指示器 */}
            <DragIndicator isOver={isOver} canDrop={canDrop} />
            {ratioMaterials.length === 0 ? (
              <div className='text-center py-12'>
                <Target className='h-12 w-12 mx-auto mb-4 text-muted-foreground' />
                <h3 className='text-lg font-medium mb-2'>开始配比设计</h3>
                <p className='text-muted-foreground mb-4'>从左侧物料仓拖拽材料到这里开始配比设计</p>
                <div className='flex items-center justify-center gap-2 text-sm text-muted-foreground'>
                  <div className='w-2 h-2 bg-primary rounded-full animate-pulse' />
                  <span>等待拖拽材料...</span>
                </div>
              </div>
            ) : (
              <div className='flex flex-col h-full'>
                {/* 表头 */}
                <div className='grid grid-cols-8 gap-2 p-2 border-b text-xs font-medium text-muted-foreground bg-muted/20 sticky top-0'>
                  <div>材料信息</div>
                  <div>理论量</div>
                  <div>含水率</div>
                  <div>含石率</div>
                  <div>实际量</div>
                  <div>设计值</div>
                  <div>料仓</div>
                  <div>操作</div>
                </div>

                {/* 材料行 - 可滚动区域 */}
                <div className='flex-1 overflow-y-auto max-h-[300px] space-y-1 p-2'>
                  {ratioMaterials.map(material => (
                    <MaterialRow
                      key={material.id}
                      material={material}
                      onUpdate={handleMaterialUpdate}
                      onDelete={handleMaterialDelete}
                    />
                  ))}
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
